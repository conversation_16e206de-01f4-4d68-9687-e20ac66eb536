// src/db/schema.ts
import { sql } from "drizzle-orm";
import {
  integer,
  sqliteTable,
  text,
  uniqueIndex,
} from "drizzle-orm/sqlite-core";

// Users (Kunden deines Dienstes)
export const users = sqliteTable("users", {
  id: text("id").primaryKey(), // z.B. auth0|xyz..., oder eigene UUID
  email: text("email").unique(),
  name: text("name"),
  avatar: text("avatar"),
  createdAt: integer("created_at", { mode: "timestamp_ms" }).default(
    sql`(unixepoch('now', 'subsec') * 1000)`
  ),
  updatedAt: integer("updated_at", { mode: "timestamp_ms" }).default(
    sql`(unixepoch('now', 'subsec') * 1000)`
  ),
});

// Organizations/Teams table
export const organizations = sqliteTable("organizations", {
  id: text("id").primary<PERSON>ey(),
  name: text("name").notNull(),
  slug: text("slug").notNull().unique(),
  description: text("description"),
  logo: text("logo"),
  paddle_customer_id: text("paddle_customer_id"),
  paddle_subscription_id: text("paddle_subscription_id"),
  plan: text("plan", { enum: ["standard", "pro", "enterprise"] })
    .notNull()
    .default("standard"),
  status: text("status", {
    enum: ["active", "trialing", "past_due", "unpaid", "canceled", "inactive"],
  })
    .notNull()
    .default("inactive"),
  current_period_end: integer("current_period_end", { mode: "timestamp" }),
  billingEmail: text("billing_email"),
  createdAt: integer("created_at", { mode: "timestamp" })
    .notNull()
    .default(sql`(unixepoch())`),
  updatedAt: integer("updated_at", { mode: "timestamp" })
    .notNull()
    .default(sql`(unixepoch())`),
});

// Organization members - many-to-many relationship between users and organizations
export const organizationMembers = sqliteTable("organization_members", {
  id: text("id").primaryKey(),
  organizationId: text("organization_id")
    .notNull()
    .references(() => organizations.id, { onDelete: "cascade" }),
  userId: text("user_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  role: text("role", { enum: ["owner", "admin", "member"] })
    .notNull()
    .default("member"),
  invitedBy: text("invited_by").references(() => users.id),
  invitedAt: integer("invited_at", { mode: "timestamp" }),
  joinedAt: integer("joined_at", { mode: "timestamp" })
    .notNull()
    .default(sql`(unixepoch())`),
  createdAt: integer("created_at", { mode: "timestamp" })
    .notNull()
    .default(sql`(unixepoch())`),
});

// Feeds (vom Nutzer erstellt)
export const feeds = sqliteTable("feeds", {
  id: text("id").primaryKey(), // z.B. uuid()
  createdBy: text("user_id").notNull(), // User who created the feed
  organizationId: text("organization_id").notNull(), // PropelAuth organization ID
  name: text("name").notNull(),
  description: text("description"),
  status: text("status", { enum: ["active", "paused", "archived"] })
    .notNull()
    .default("active"),
  createdAt: integer("created_at", { mode: "timestamp_ms" }).default(
    sql`(unixepoch('now', 'subsec') * 1000)`
  ),
  updatedAt: integer("updated_at", { mode: "timestamp_ms" }).default(
    sql`(unixepoch('now', 'subsec') * 1000)`
  ),
});

// Platform Connections (OAuth Verknüpfungen zu sozialen Netzwerken)
export const platformConnections = sqliteTable(
  "platform_connections",
  {
    id: text("id").primaryKey(), // z.B. uuid()
    feedId: text("feed_id")
      .notNull()
      .references(() => feeds.id, { onDelete: "cascade" }), // Beziehung zu feeds
    platform: text("platform", {
      enum: ["instagram", "instagram_business", "facebook", "tiktok"],
    }).notNull(),
    platformAccountId: text("platform_account_id").notNull(), // z.B. Instagram User ID, Facebook Page ID
    platformAccountName: text("platform_account_name"), // z.B. Instagram @username, FB Page Name
    apiType: text("api_type", { enum: ["graph", "basic_display"] })
      .notNull()
      .default("graph"), // API-Typ für Instagram (Graph API vs Basic Display API)
    accessTokenEncrypted: text("access_token_encrypted"), // Tokens IMMER verschlüsselt
    refreshTokenEncrypted: text("refresh_token_encrypted"), // Nur falls relevant (IG hat keine Refresh Tokens für Long-Lived)
    scopes: text("scopes"), // Gewährte Berechtigungen als Text (z.B. komma-separiert)
    status: text("status", {
      enum: ["active", "inactive", "reauth_needed", "expired"],
    }).default("active"), // Status der Verbindung
    createdAt: integer("created_at", { mode: "timestamp_ms" }).default(
      sql`(unixepoch('now', 'subsec') * 1000)`
    ),
    tokenExpiresAt: integer("token_expires_at", { mode: "timestamp_ms" }), // Ungefähres Ablaufdatum (falls bekannt)
    lastCheckedAt: integer("last_checked_at", { mode: "timestamp_ms" }), // Token-Gültigkeit zuletzt geprüft
    lastPolledAt: integer("last_polled_at", { mode: "timestamp_ms" }), // Nur für Basic Display Polling relevant
  },
  (table) => ({
    // Index für schnelle Suche nach platformAccountId (wichtig für Webhook und Token Check)
    platformAccountIdx: uniqueIndex("platform_account_idx").on(
      table.platform,
      table.platformAccountId
    ),
    // Index für schnelle Suche nach Connections pro Feed
    feedIdx: uniqueIndex("connection_feed_idx").on(table.feedId),
    // Sicherstellen, dass eine Plattform pro Feed nur einmal vorkommt (optional, je nach Logik)
    // feedPlatformUnique: uniqueIndex('feed_platform_unique_idx').on(table.feedId, table.platform, table.platformAccountId),
  })
);

// API Keys (Zugriff auf einen spezifischen Feed)
export const apiKeys = sqliteTable(
  "api_keys",
  {
    key: text("key").primaryKey(), // Der API Key selbst (Hash wäre sicherer)
    feedId: text("feed_id")
      .notNull()
      .references(() => feeds.id, { onDelete: "cascade" }), // Beziehung zu feeds
    createdBy: text("created_by")
      .notNull()
      .references(() => users.id),
    description: text("description"),
    status: text("status", { enum: ["active", "inactive", "revoked"] }).default(
      "active"
    ),
    keyPreview: text("key_preview").notNull(),
    createdAt: integer("created_at", { mode: "timestamp_ms" }).default(
      sql`(unixepoch('now', 'subsec') * 1000)`
    ),
    updatedAt: integer("updated_at", { mode: "timestamp_ms" }).default(
      sql`(unixepoch('now', 'subsec') * 1000)`
    ),
    expiresAt: integer("expires_at", { mode: "timestamp_ms" }),
    lastUsedAt: integer("last_used_at", { mode: "timestamp_ms" }),
  },
  (table) => ({
    feedIdx: uniqueIndex("apikey_feed_idx").on(table.feedId),
  })
);

// Posts (Verweist jetzt auf platformConnections)
export const posts = sqliteTable(
  "posts",
  {
    id: text("id").primaryKey(), // Eigene UUID
    platformConnectionId: text("platform_connection_id")
      .notNull()
      .references(() => platformConnections.id, { onDelete: "cascade" }), // Beziehung
    mediaId: text("media_id").notNull(), // ID des Mediums auf der Plattform
    likeCount: integer("like_count").default(0),
    commentsCount: integer("comments_count").default(0),
    caption: text("caption"),
    mediaUrl: text("media_url"),
    mediaType: text("media_type"), // Z.B. IMAGE, VIDEO, CAROUSEL_ALBUM
    permalink: text("permalink"), // Link zum Post
    timestamp: integer("timestamp", { mode: "timestamp_ms" }), // Erstellungszeitpunkt des Posts auf der Plattform
    lastWebhookUpdate: integer("last_webhook_update", { mode: "timestamp_ms" }),
    lastFetched: integer("last_fetched", { mode: "timestamp_ms" }),
  },
  (table) => ({
    connectionMediaIdx: uniqueIndex("connection_media_idx").on(
      table.platformConnectionId,
      table.mediaId
    ), // Wichtig für Upserts
    connectionTimestampIdx: uniqueIndex("connection_timestamp_idx").on(
      table.platformConnectionId,
      table.timestamp
    ), // Für Sortierung
  })
);

// Comments (Kommentare zu Posts)
export const comments = sqliteTable(
  "comments",
  {
    id: text("id").primaryKey(), // Eigene UUID
    platformConnectionId: text("platform_connection_id")
      .notNull()
      .references(() => platformConnections.id, { onDelete: "cascade" }), // Beziehung
    commentId: text("comment_id").notNull(), // ID des Kommentars auf der Plattform
    text: text("text").notNull(), // Kommentartext
    userId: text("user_id"), // User ID des Kommentators auf der Plattform
    username: text("username"), // Username des Kommentators
    timestamp: integer("timestamp", { mode: "timestamp_ms" }), // Erstellungszeitpunkt des Kommentars
  },
  (table) => ({
    connectionCommentIdx: uniqueIndex("connection_comment_idx").on(
      table.platformConnectionId,
      table.commentId
    ), // Wichtig für Upserts
  })
);

// Generated links for social media connections
export const generatedLinks = sqliteTable("generated_links", {
  id: text("id").primaryKey(),
  feedId: text("project_id")
    .notNull()
    .references(() => feeds.id, { onDelete: "cascade" }),
  platform: text("platform"), // JSON Array of platforms
  token: text("token").notNull().unique(),
  name: text("name").notNull(),
  description: text("description"),
  redirectUrl: text("redirect_url"),
  expiresAt: integer("expires_at", { mode: "timestamp" }),
  isActive: integer("is_active", { mode: "boolean" }).notNull().default(true),
  createdBy: text("created_by")
    .notNull()
    .references(() => users.id),
  createdAt: integer("created_at", { mode: "timestamp_ms" })
    .notNull()
    .default(sql`(unixepoch('now', 'subsec') * 1000)`),
  updatedAt: integer("updated_at", { mode: "timestamp_ms" })
    .notNull()
    .default(sql`(unixepoch('now', 'subsec') * 1000)`),
});

{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"build": "next build", "dev": "next dev", "lint": "next lint", "start": "next start"}, "dependencies": {"@aws-sdk/client-rds-data": "latest", "@cloudflare/workers-types": "^4.20250618.0", "@electric-sql/pglite": "latest", "@hookform/resolvers": "^3.9.1", "@libsql/client": "latest", "@libsql/client-wasm": "latest", "@neondatabase/serverless": "latest", "@op-engineering/op-sqlite": "latest", "@opentelemetry/api": "latest", "@planetscale/database": "latest", "@prisma/client": "latest", "@propelauth/nextjs": "latest", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@tidbcloud/serverless": "latest", "@types/better-sqlite3": "latest", "@types/pg": "latest", "@types/sql.js": "latest", "@upstash/redis": "latest", "@vercel/postgres": "latest", "@xata.io/client": "latest", "autoprefixer": "^10.4.20", "better-sqlite3": "latest", "bun-types": "latest", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "4.1.0", "drizzle-orm": "^0.44.2", "embla-carousel-react": "8.5.1", "expo-sqlite": "latest", "gel": "latest", "input-otp": "1.4.1", "knex": "latest", "kysely": "latest", "lucide-react": "^0.454.0", "mysql2": "latest", "next": "15.2.4", "next-themes": "^0.4.4", "pg": "latest", "postgres": "latest", "react": "^19", "react-day-picker": "8.10.1", "react-dom": "^19", "react-hook-form": "^7.54.1", "react-resizable-panels": "^2.1.7", "recharts": "2.15.0", "sonner": "^1.7.1", "sql.js": "latest", "sqlite3": "latest", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.6", "zod": "^3.24.1"}, "devDependencies": {"@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "postcss": "^8.5", "tailwindcss": "^3.4.17", "typescript": "^5"}}
{"name": "api", "version": "0.2.0", "private": true, "scripts": {"dev": "wrangler dev src/index.ts", "deploy": "wrangler deploy src/index.ts", "db:generate": "drizzle-kit generate:sqlite", "db:migrate:local": "wrangler d1 migrations apply socialfeed-test --local", "db:migrate:prod": "wrangler d1 migrations apply socialfeed-test --remote", "logs": "wrangler tail", "check-types": "tsc --noEmit"}, "dependencies": {"@repo/drizzle-schema": "workspace:*", "drizzle-orm": "^0.44.2", "hono": "^4.7.7", "jose": "^5.10.0", "zod": "^3.24.3"}, "devDependencies": {"@cloudflare/workers-types": "^4.20250618.0", "@types/node": "^20.17.31", "drizzle-kit": "^0.20.18", "typescript": "^5.8.3", "wrangler": "^3.114.7"}, "module": "src/index.ts"}
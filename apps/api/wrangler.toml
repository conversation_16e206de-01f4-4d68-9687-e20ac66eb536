# wrangler.toml
name = "cloudflare-social-aggregator" # Passe den Namen deines Workers an
main = "src/index.ts"
compatibility_date = "2024-04-26" # Verwende aktuelles Datum
compatibility_flags = [ "nodejs_compat" ]

# Bindings
[[d1_databases]]
binding = "DB" # Name für den Zugriff im Code
database_name = "socialfeed-test" # Muss im Cloudflare Dashboard existieren
database_id = "d4c5c514-a8cb-4628-91a5-338f567afa1d"     # Finde diese ID im Dashboard
migrations_dir = "drizzle/migrations"

[[durable_objects.bindings]]
name = "DEBOUNCER_DO" # Name für den Zugriff im Code
class_name = "DebouncerDO" # Name der exportierten Klasse in src/debouncer-do.ts

# Drizzle Migrations Modul - Konfiguration über wrangler d1 migrations ... wird empfohlen
# --- NEU / WICHTIG: Diese Sektion für DO Klassen ---
# Dieser Block teilt Cloudflare mit, welche Klassen DOs sind.
[[migrations]]
tag = "v1-add-debouncer-do"    # Ein eindeutiger Name/Tag für diesen Migrationsschritt (frei wählbar)
new_classes = ["DebouncerDO"] # Liste der *Namen* der *neuen* oder *geänderten* DO-Klassen in diesem Deployment. Muss exakt mit dem Export übereinstimmen!

[[analytics_engine_datasets]]
binding = "USAGE_ANALYTICS" # Name im Code
dataset = "api_usage"       # Muss im Dashboard erstellt werden

[[analytics_engine_datasets]]
binding = "APP_ERRORS"      # Name im Code
dataset = "app_errors"      # Muss im Dashboard erstellt werden

# Queues
[[queues.producers]]
queue = "pagination-dlq"     # Name der DLQ
binding = "PAGINATION_DLQ" # Optionales Binding

[[queues.producers]]
queue = "pagination-queue"   # Name der Haupt-Queue
binding = "PAGINATION_QUEUE" # Binding für den Produzenten (DO)

[[queues.consumers]]
queue = "pagination-queue"     # Name der Queue, die konsumiert wird
max_batch_size = 10
max_batch_timeout = 5
dead_letter_queue = "pagination-dlq" # Verweist auf die DLQ (muss existieren)
max_retries = 5                     # Anzahl Wiederholungen bevor DLQ

[[queues.producers]]
queue = "sync-dlq"
binding = "SYNC_DLQ" # Optionales Binding

[[queues.producers]]
queue = "sync-queue"
binding = "SYNC_QUEUE"

[[queues.consumers]]
queue = "sync-queue"
max_batch_size = 5
max_batch_timeout = 2
dead_letter_queue = "sync-dlq" # Muss existieren
max_retries = 3

# Secrets (Platzhalter - Setze diese mit `wrangler secret put SECRET_NAME`)
[vars]

# YOUR_VERIFY_TOKEN = "..." # Das Token, das du bei Meta für die Webhook Verifizierung einträgst
# APP_ID = "..." # Deine Meta App ID
# INSTAGRAM_APP_SECRET = "..."
# ENCRYPTION_KEY = "..." # Dein 64-Zeichen HEX Key für AES-GCM
# JWT_SECRET = "..." # Dein Secret für die User JWT Signatur (wenn HS256 genutzt wird)

[placement]
mode = "smart" # Empfohlen für DOs

# Cron Triggers
[triggers]
crons = [
    "5 3 * * *",          # Täglich um 03:05 UTC (Token Check)
    "*/5 * * * *"         # Alle 5 Minuten (Basic Display Poll)
]
{"compilerOptions": {"target": "ESNext", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "lib": ["ESNext"], "strict": true, "skipLibCheck": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "outDir": "./dist", "jsx": "react-jsx", "jsxImportSource": "hono/jsx", "types": ["@cloudflare/workers-types"]}, "include": ["src/**/*.ts", "drizzle.config.ts"], "exclude": ["node_modules", "dist"]}
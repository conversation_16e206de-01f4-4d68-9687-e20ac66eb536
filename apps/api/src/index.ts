// src/index.ts
import { Hono, Next } from "hono";
import { HTTPException } from "hono/http-exception";
import { z } from "zod";
import * as jose from "jose";
import * as schema from "@repo/drizzle-schema/d1";

import * as constants from "./constants";

// Importiere alle Typen und Services
import {
  AppContext,
  Bindings,
  InstagramWebhookPayload,
  UserDeauthorizationPayload,
  WebhookChange,
  WebhookSignalData,
  PaginationQueueMessage,
  SyncQueueMessage,
  PlatformConnection,
  Platform,
  UserJwtPayload,
  ApiType,
  ConnectionStatus,
  WebhookCommentVerb,
  GraphApiComment,
  GraphApiMedia,
  InsertPost,
  InsertComment,
  GraphApiMeSchema,
} from "./types";
import {
  getDbClient,
  findActiveConnectionsByPlatformUserId,
  validateApiKey,
  updateConnectionStatus,
  getPostsForFeed,
  isConnectionOwnedByUser,
  upsertPost,
  upsertComment,
  deleteComment,
  getConnectionDetails,
  getActiveBasicDisplayConnections,
  getConnectionsForTokenCheck,
} from "./database-service";
import { DebouncerDO } from "./debouncer-do";
import { logErrorToAnalytics } from "./analytics-utils";
import {
  fetchNextCommentPage,
  GraphApiError,
  checkBasicDisplayToken,
  fetchMediaDataAndFirstComments,
  fetchBasicDisplayMedia,
  fetchLatestGraphMedia,
} from "./graph-api";
import { decryptToken } from "./token-utils";
import { timing } from "hono/timing";
import { secureHeaders } from "hono/secure-headers";
import { ContentfulStatusCode } from "hono/utils/http-status";
import { desc, eq } from "drizzle-orm";
import { DrizzleD1Database } from "drizzle-orm/d1";

const app = new Hono<AppContext>();

// --- Middlewares ---
app.use("*", timing());
app.use("*", secureHeaders()); // Fügt Security Header hinzu

// Organization context middleware - extracts organization from JWT
app.use("*", async (c, next) => {
  const authHeader = c.req.header("Authorization");
  const bearerPrefix = "Bearer ";

  if (authHeader && authHeader.startsWith(bearerPrefix)) {
    const token = authHeader.substring(bearerPrefix.length);
    const secret = c.env.JWT_SECRET;

    if (token && secret) {
      try {
        const secretKey = new TextEncoder().encode(secret);
        const { payload } = await jose.jwtVerify<UserJwtPayload>(
          token,
          secretKey
        );

        if (payload.sub) {
          c.set("userId", payload.sub);

          // Extract organization context
          if (payload.org_id) {
            c.set("organizationId", payload.org_id);
            c.set("userRole", payload.org_role || "Member");
            console.log(
              `Organization context set: ${payload.org_id} (${payload.org_role || "Member"})`
            );
          }
        }
      } catch (error) {
        // Don't throw error here, just log it - this allows API key auth to still work
        console.warn("JWT validation failed in org context middleware:", error);
      }
    }
  }

  await next();
});

// API Key Auth Middleware for /feeds/ endpoints
app.use("/feeds/:feedId/*", async (c, next) => {
  console.log("API Key Auth Middleware running...");
  const authHeader = c.req.header("Authorization");
  const bearerPrefix = "Bearer ";
  if (!authHeader || !authHeader.startsWith(bearerPrefix))
    throw new HTTPException(401, {
      message: "Missing or invalid Authorization header",
    });
  const apiKey = authHeader.substring(bearerPrefix.length);
  if (!apiKey) throw new HTTPException(401, { message: "API key missing" });
  try {
    const db = getDbClient(c.env.DB);
    const validationResult = await validateApiKey(db, apiKey);
    if (!validationResult.isValid || !validationResult.feedId) {
      throw new HTTPException(
        (validationResult.status as ContentfulStatusCode) ?? 401,
        {
          message: validationResult.error,
        }
      );
    }
    // Authorization check: Does the feed ID in the path match the one the key is authorized for?
    const requestedFeedId = c.req.param("feedId");
    if (requestedFeedId && validationResult.feedId !== requestedFeedId) {
      console.warn(
        `API Key valid for feed ${validationResult.feedId} but requested feed ${requestedFeedId}`
      );
      logErrorToAnalytics(
        c.env,
        "AUTH_APIKEY_FEED_MISMATCH",
        "API Key not valid for requested feed",
        {
          keyPrefix: apiKey.substring(0, 3),
          requestedFeedId,
          authorizedFeedId: validationResult.feedId,
        }
      );
      throw new HTTPException(403, {
        message: "API key not valid for this feed",
      });
    }
    c.set("authorizedFeedId", validationResult.feedId);
    c.set("apiKey", apiKey);

    // Set organization context from API key validation
    if (validationResult.organizationId) {
      c.set("organizationId", validationResult.organizationId);
    }

    console.log(
      `API Key validated for feed: ${validationResult.feedId}, org: ${validationResult.organizationId || "unknown"}`
    );
  } catch (e) {
    if (e instanceof HTTPException) throw e;
    console.error("API key validation error:", e);
    logErrorToAnalytics(
      c.env,
      "AUTH_APIKEY_ERROR",
      "API key validation failed",
      { error: String(e) }
    );
    throw new HTTPException(500, {
      message: "Internal server error during authentication",
    });
  }
  await next();
});

// User Session Auth Middleware via JWT (for protected endpoints)
app.use("/connections/:platformConnectionId/*", async (c, next) => {
  console.log("User JWT Auth Middleware running...");

  // Check if user is authenticated
  if (!c.var.userId) {
    const authHeader = c.req.header("Authorization");
    const bearerPrefix = "Bearer ";
    if (!authHeader || !authHeader.startsWith(bearerPrefix))
      throw new HTTPException(401, {
        message:
          "Missing or invalid Authorization header (Bearer JWT expected)",
      });
    const token = authHeader.substring(bearerPrefix.length);
    if (!token) throw new HTTPException(401, { message: "Token missing" });
    const secret = c.env.JWT_SECRET;
    if (!secret) {
      console.error("JWT_SECRET not configured!");
      throw new HTTPException(500, { message: "Internal server config error" });
    }
    const secretKey = new TextEncoder().encode(secret);
    try {
      const { payload } = await jose.jwtVerify<UserJwtPayload>(
        token,
        secretKey /*, {issuer:'...', audience:'...'}*/
      );
      if (!payload.sub) throw new Error("JWT payload missing subject (sub)");
      c.set("userId", payload.sub);

      // Extract organization context if not already set
      if (payload.org_id && !c.var.organizationId) {
        c.set("organizationId", payload.org_id);
        c.set("userRole", payload.org_role || "Member");
      }

      console.log(
        `User JWT validated for user: ${payload.sub}, org: ${payload.org_id || "none"}`
      );
    } catch (error: any) {
      console.error("JWT validation failed:", error);
      let message = "Invalid token";
      if (error.code === "ERR_JWT_EXPIRED") message = "Token has expired";
      logErrorToAnalytics(
        c.env,
        "AUTH_JWT_ERROR",
        `JWT Validation failed: ${message}`,
        { error: error?.code || String(error) }
      );
      throw new HTTPException(401, { message: message });
    }
  }

  // Check if organization context is available
  if (!c.var.organizationId) {
    throw new HTTPException(403, {
      message:
        "Organization context required. Make sure your token includes organization information.",
    });
  }

  await next();
});

// Webhook Verification Middleware
app.use("/webhook", async (c, next) => {
  if (c.req.method === "GET") {
    const mode = c.req.query("hub.mode");
    const token = c.req.query("hub.verify_token");
    const challenge = c.req.query("hub.challenge");
    const verifyToken = c.env.YOUR_VERIFY_TOKEN; // Aus Env/Secrets holen
    if (!verifyToken) {
      console.error("YOUR_VERIFY_TOKEN not set!");
      return c.text("Config Error", 500);
    }
    if (mode === "subscribe" && token === verifyToken && challenge) {
      console.log("Webhook verification successful!");
      return c.text(challenge);
    } else {
      console.error("Webhook verification failed.");
      throw new HTTPException(403, { message: "Forbidden" });
    }
  }
  if (c.req.method === "POST") {
    const signatureHeader = c.req.header("X-Hub-Signature-256");
    if (!signatureHeader) {
      console.error("Missing signature");
      logErrorToAnalytics(
        c.env,
        "WEBHOOK_AUTH_FAIL",
        "Missing X-Hub-Signature-256"
      );
      throw new HTTPException(401, {
        message: "Unauthorized: Missing signature",
      });
    }
    const rawBody = await c.req.raw.clone().text(); // Klonen wichtig!
    try {
      const secret = c.env.INSTAGRAM_APP_SECRET;
      if (!secret) throw new Error("INSTAGRAM_APP_SECRET not configured");
      const encoder = new TextEncoder();
      const key = await crypto.subtle.importKey(
        "raw",
        encoder.encode(secret),
        { name: "HMAC", hash: "SHA-256" },
        false,
        ["verify"]
      );
      const signature = hexToBytes(signatureHeader.replace("sha256=", ""));
      const isValid = await crypto.subtle.verify(
        "HMAC",
        key,
        signature,
        encoder.encode(rawBody)
      );
      if (!isValid) {
        console.error("Invalid signature");
        logErrorToAnalytics(c.env, "WEBHOOK_AUTH_FAIL", "Invalid signature");
        throw new HTTPException(401, {
          message: "Unauthorized: Invalid signature",
        });
      }
      console.log("Webhook signature verified.");
    } catch (error) {
      console.error("Signature verification error:", error);
      logErrorToAnalytics(
        c.env,
        "WEBHOOK_AUTH_ERROR",
        "Error during signature verification",
        { error: String(error) }
      );
      throw new HTTPException(500, { message: "Verification Error" });
    }
  }
  await next();
});
// Hilfsfunktion Hex To Bytes
function hexToBytes(hex: string): Uint8Array {
  if (hex.length % 2 !== 0) throw new Error("Invalid hex string length.");
  const bytes = new Uint8Array(hex.length / 2);
  for (let i = 0; i < hex.length; i += 2) {
    bytes[i / 2] = parseInt(hex.substring(i, i + 2), 16);
    if (isNaN(bytes[i / 2])) throw new Error("Invalid hex character found.");
  }
  return bytes;
}

// --- Zod Schemas für Webhooks ---
const InstagramMediaValueSchema = z
  .object({
    media_id: z.string(),
    comment_id: z.string().optional(),
    text: z.string().optional(),
    verb: z
      .enum(["add", "edit", "edited", "remove", "delete", "deleted"])
      .optional(),
  })
  .passthrough();
const InstagramChangeSchema = z.object({
  field: z.string(),
  value: InstagramMediaValueSchema,
});
const InstagramEntrySchema = z.object({
  id: z.string(),
  time: z.number(),
  changes: z.array(InstagramChangeSchema),
});
const InstagramWebhookPayloadSchema = z.object({
  object: z.literal("instagram"),
  entry: z.array(InstagramEntrySchema),
});
const UserDeauthValueSchema = z
  .object({ user_id: z.string().optional() })
  .passthrough();
const UserDeauthChangeSchema = z.object({
  field: z.literal("permissions"),
  value: UserDeauthValueSchema,
}); // Field ist oft 'permissions'
const UserDeauthEntrySchema = z.object({
  id: z.string(),
  time: z.number(),
  changes: z.array(UserDeauthChangeSchema),
});
const UserDeauthPayloadSchema = z.object({
  object: z.literal("user"),
  entry: z.array(UserDeauthEntrySchema),
});

// --- Webhook Endpoint ---
app.post("/webhook", async (c) => {
  let body: any;
  try {
    body = await c.req.json();
  } catch (e) {
    logErrorToAnalytics(c.env, "WEBHOOK_PARSE_ERROR", "Failed JSON parse", {
      error: String(e),
    });
    return c.text("OK", 200);
  }

  // ---- Instagram Media Updates ----
  if (body.object === "instagram") {
    const validationResult = InstagramWebhookPayloadSchema.safeParse(body);
    if (!validationResult.success) {
      logErrorToAnalytics(
        c.env,
        "WEBHOOK_VALIDATION_ERROR",
        "Invalid Instagram payload",
        { errors: validationResult.error.flatten() }
      );
      return c.text("OK", 200);
    }
    const validData = validationResult.data;
    const db = getDbClient(c.env.DB);
    for (const entry of validData.entry) {
      const instagramUserId = entry.id;
      try {
        const connections = await findActiveConnectionsByPlatformUserId(
          db,
          "instagram",
          instagramUserId
        );
        if (connections.length === 0) {
          console.warn(
            `Webhook: No active connection for IG User ${instagramUserId}.`
          );
          continue;
        }
        for (const connection of connections) {
          const platformConnectionId = connection.id;
          for (const change of entry.changes) {
            const field = change.field;
            const value = change.value;
            if (
              (field === "comments" ||
                field === "reactions" ||
                field === "likes") &&
              value.media_id
            ) {
              const mediaId = value.media_id;
              const doId = c.env.DEBOUNCER_DO.idFromName(platformConnectionId);
              const stub = c.env.DEBOUNCER_DO.get(doId);
              console.log(
                `Dispatching signal for media ${mediaId} (field: ${field}, verb: ${value.verb}) to DO ${platformConnectionId}`
              );
              // Signal enthält jetzt nicht mehr platformConnectionId, da diese die DO ID ist
              const signalData: Omit<
                WebhookSignalData,
                "platformConnectionId"
              > = {
                mediaId,
                field,
                verb: value.verb,
                commentId: value.comment_id,
              };
              // Erzeuge das URL-Objekt aus der Anfrage-URL
              const requestUrl = new URL(c.req.url);
              // Konstruiere die Ziel-URL für das DO Signal mit dem korrekten Hostnamen
              const signalUrl = `https://${requestUrl.hostname}/signal`;
              c.executionCtx.waitUntil(
                stub
                  .fetch(
                    new Request(signalUrl, {
                      method: "POST",
                      headers: { "Content-Type": "application/json" },
                      body: JSON.stringify(signalData),
                    })
                  )
                  .catch((e) => {
                    console.error(
                      `Dispatch Error DO ${platformConnectionId}:`,
                      e
                    );
                    logErrorToAnalytics(
                      c.env,
                      "DO_DISPATCH_ERROR",
                      `Failed dispatch to DO`,
                      { platformConnectionId, mediaId, error: String(e) }
                    );
                  })
              );
            }
          }
        }
      } catch (dbError) {
        console.error("Webhook DB Error:", dbError);
        logErrorToAnalytics(
          c.env,
          "WEBHOOK_DB_LOOKUP_ERROR",
          "Webhook DB lookup failed",
          { instagramUserId, error: String(dbError) }
        );
      }
    }
  }
  // ---- User Deauthorization ----
  else if (body.object === "user") {
    const validationResult = UserDeauthPayloadSchema.safeParse(body);
    if (!validationResult.success) {
      logErrorToAnalytics(
        c.env,
        "WEBHOOK_VALIDATION_ERROR",
        "Invalid User Deauth payload",
        { errors: validationResult.error.flatten() }
      );
      return c.text("OK", 200);
    }
    const validData = validationResult.data;
    const db = getDbClient(c.env.DB);
    for (const entry of validData.entry) {
      // Die 'id' im entry ist hier die globale Meta User ID
      const metaUserId = entry.id;
      console.log(
        `Received deauthorization signal for platform user ${metaUserId}`
      );
      try {
        // Finde und deaktiviere alle Verbindungen, die diesem Meta User zugeordnet sind
        const result = await db
          .update(schema.platformConnections)
          .set({
            status: "inactive",
            accessTokenEncrypted: null,
            refreshTokenEncrypted: null,
          }) // Token auch löschen
          .where(eq(schema.platformConnections.platformAccountId, metaUserId)) // Annahme: platformAccountId ist Meta User ID für FB/IG
          .returning({ id: schema.platformConnections.id });
        console.log(
          `Deauthorization: Marked ${result.length} connections inactive for user ${metaUserId}`
        );
        if (result.length > 0) {
          logErrorToAnalytics(
            c.env,
            "USER_DEAUTHORIZATION",
            `User deauthorized app`,
            { metaUserId, connectionsAffected: result.map((r) => r.id) }
          );
        }
      } catch (dbError) {
        console.error(`Deauth DB Error for ${metaUserId}`, dbError);
        logErrorToAnalytics(
          c.env,
          "DEAUTH_DB_ERROR",
          `Failed DB update for deauth user ${metaUserId}`,
          { metaUserId, error: String(dbError) }
        );
      }
    }
  }
  return c.text("OK", 200);
});

// --- Datenabfrage-Endpoint (/feeds/:feedId/posts) ---
app.get("/feeds/:feedId/posts", async (c) => {
  const requestedFeedId = c.req.param("feedId");
  const authorizedFeedId = c.var.authorizedFeedId;
  if (!authorizedFeedId || requestedFeedId !== authorizedFeedId)
    throw new HTTPException(403, {
      message: "Forbidden: API key not authorized for this feed",
    });
  const apiKey = c.var.apiKey;

  // Analytics Zählung
  if (c.env.USAGE_ANALYTICS) {
    try {
      c.executionCtx.waitUntil(
        Promise.resolve()
          .then(() => {
            c.env.USAGE_ANALYTICS.writeDataPoint({
              indexes: [authorizedFeedId],
              blobs: [`/feeds/${requestedFeedId}/posts`, apiKey || ""],
              doubles: [1],
            });
            console.log(`Logged API usage for feed ${authorizedFeedId}`);
          })
          .catch((e) => console.error("Analytics Engine write failed:", e))
      );
    } catch (e) {
      console.error("Error calling Analytics Engine:", e);
    }
  }

  // Cache Logik
  const cache = caches.default;
  const cacheKey = new Request(c.req.url, c.req);
  try {
    const cachedResponse = await cache.match(cacheKey);
    if (cachedResponse) {
      console.log(`Cache HIT for: ${c.req.url}`);
      const response = new Response(cachedResponse.body, cachedResponse);
      response.headers.set("X-Cache-Status", "hit");
      response.headers.set("Access-Control-Allow-Origin", "*"); // Beispiel CORS Header
      return response;
    }
  } catch (cacheError) {
    console.error(`Cache match error for ${c.req.url}:`, cacheError);
    logErrorToAnalytics(c.env, "CACHE_MATCH_ERROR", "Cache match failed", {
      url: c.req.url,
      error: String(cacheError),
    });
  }
  console.log(`Cache MISS for: ${c.req.url}`);

  // DB Fetch Logik
  const limit = Math.min(
    Math.max(1, parseInt(c.req.query("limit") || "20", 10)),
    100
  ); // Limit validieren
  const platformFilter = c.req.query("platform") as Platform | undefined; // Ggf. Plattform validieren
  try {
    const db = getDbClient(c.env.DB);
    const results = await getPostsForFeed(
      db,
      authorizedFeedId,
      limit,
      platformFilter
    ); // DB Service
    const response = c.json(results);
    response.headers.set("Cache-Control", "public, max-age=5"); // Cache für 5s
    response.headers.set("X-Cache-Status", "miss");
    response.headers.set("Access-Control-Allow-Origin", "*"); // Beispiel CORS Header
    c.executionCtx.waitUntil(cache.put(cacheKey, response.clone())); // Cache im Hintergrund
    return response;
  } catch (e: any) {
    console.error(`Error fetching posts for feed ${authorizedFeedId}:`, e);
    logErrorToAnalytics(c.env, "GET_POSTS_ERROR", "Failed to fetch posts", {
      feedId: authorizedFeedId,
      error: String(e),
    });
    throw new HTTPException(500, { message: "Failed to fetch posts" });
  }
});

// --- Endpoint to trigger post sync ---
app.post("/connections/:platformConnectionId/sync-posts", async (c) => {
  const platformConnectionId = c.req.param("platformConnectionId");
  const loggedInUserId = c.var.userId;
  const organizationId = c.var.organizationId;

  if (!loggedInUserId)
    throw new HTTPException(401, { message: "User authentication required" });

  if (!organizationId)
    throw new HTTPException(403, { message: "Organization context required" });

  // Authorization: Check if connection belongs to user's organization
  const db = getDbClient(c.env.DB);
  try {
    const isOrgOwner = await isConnectionOwnedByOrganization(
      db,
      platformConnectionId,
      organizationId
    );

    if (!isOrgOwner)
      throw new HTTPException(403, {
        message:
          "Forbidden: This connection does not belong to your organization",
      });

    const connection = await getConnectionDetails(db, platformConnectionId);
    if (!connection || connection.status !== "active")
      throw new HTTPException(400, {
        message: "Connection is not active or not found",
      });
  } catch (e) {
    console.error(
      `Error checking ownership/status for conn ${platformConnectionId}:`,
      e
    );
    logErrorToAnalytics(c.env, "SYNC_OWNERSHIP_CHECK_ERROR", `Failed check`, {
      platformConnectionId,
      userId: loggedInUserId,
      organizationId,
      error: String(e),
    });
    throw new HTTPException(500, { message: "Internal server error" });
  }

  // Enqueue Task
  try {
    const message: SyncQueueMessage = {
      platformConnectionId,
      triggeredByUserId: loggedInUserId,
      organizationId,
    };
    await c.env.SYNC_QUEUE.send(message);
    console.log(
      `Enqueued post sync task for connection: ${platformConnectionId}`
    );
    return c.json({ message: "Post synchronization started." }, 202);
  } catch (e) {
    console.error(
      `Failed to enqueue sync task for conn ${platformConnectionId}:`,
      e
    );
    logErrorToAnalytics(c.env, "SYNC_ENQUEUE_ERROR", `Failed queue send`, {
      platformConnectionId,
      userId: loggedInUserId,
      organizationId,
      error: String(e),
    });
    throw new HTTPException(500, {
      message: "Failed to start synchronization",
    });
  }
});

// --- Queue Consumer für Paginierung ---
async function processPaginationQueue(
  batch: MessageBatch<PaginationQueueMessage>,
  env: Bindings,
  ctx: ExecutionContext
): Promise<void> {
  console.log(`Processing ${batch.messages.length} pagination messages...`);
  const db = getDbClient(env.DB);
  const promises = batch.messages.map(async (message) => {
    const { platformConnectionId, mediaId, nextPageUrl } = message.body;
    console.log(
      `Processing next page for pcId=${platformConnectionId} mediaId=${mediaId}`
    );
    try {
      const connection = await getConnectionDetails(db, platformConnectionId);
      if (
        !connection ||
        connection.status !== "active" ||
        !connection.accessTokenEncrypted
      ) {
        console.warn(
          `Queue Pag: Conn ${platformConnectionId} inactive/no token.`
        );
        return;
      }
      const accessToken = await decryptToken(
        connection.accessTokenEncrypted,
        env
      );
      if (!accessToken) {
        await updateConnectionStatus(db, platformConnectionId, "reauth_needed");
        logErrorToAnalytics(
          env,
          "QUEUE_PAGINATION_DECRYPT_FAIL",
          `Failed decrypt token`,
          { platformConnectionId, mediaId }
        );
        return;
      }

      // API Call via Service
      const pageResult = await fetchNextCommentPage(nextPageUrl, env);

      if (!pageResult) {
        const currentStatus = await getConnectionDetails(
          db,
          platformConnectionId
        );
        if (currentStatus?.status === "reauth_needed") return; // Kein Retry bei Auth Fehler
        throw new Error(`Failed to fetch next comment page (API Client Error)`);
      }
      const { comments: commentsToWrite, nextPageUrl: subsequentNextPageUrl } =
        pageResult;

      // DB Write via Service
      if (commentsToWrite.length > 0) {
        console.log(
          `Queue Pag: Writing ${commentsToWrite.length} comments for media ${mediaId}`
        );
        for (const comment of commentsToWrite) {
          const commentData: InsertComment = {
            id: crypto.randomUUID(),
            platformConnectionId,
            commentId: comment.id,
            mediaId,
            userId: comment.from?.id || "unknown",
            username: comment.from?.username,
            text: comment.text,
            timestamp: new Date(comment.timestamp),
          };
          try {
            await upsertComment(db, commentData);
          } catch (d1Error) {
            logErrorToAnalytics(
              env,
              "QUEUE_COMMENT_UPSERT_ERROR",
              `Error upserting paginated comment ${comment.id}`,
              {
                platformConnectionId,
                mediaId,
                commentId: comment.id,
                error: String(d1Error),
              }
            );
          }
        }
      }
      // Enqueue next page
      if (subsequentNextPageUrl) {
        const nextMessage: PaginationQueueMessage = {
          platformConnectionId,
          mediaId,
          nextPageUrl: subsequentNextPageUrl,
        };
        await env.PAGINATION_QUEUE.send(nextMessage);
      }
    } catch (error: any) {
      console.error(
        `Queue Pag: Failed processing message for pcId=${platformConnectionId} mediaId=${mediaId}, attempt ${message.attempts}:`,
        error
      );
      const maxRetries = parseInt(env.QUEUE_MAX_RETRIES || "5", 10);
      if (message.attempts >= maxRetries) {
        logErrorToAnalytics(
          env,
          "QUEUE_FINAL_FAILURE",
          `Pagination message failed all retries`,
          {
            platformConnectionId,
            mediaId,
            nextPageUrl,
            attempts: message.attempts,
            error: String(error),
          }
        );
      } else {
        message.retry({ delaySeconds: 30 * (message.attempts + 1) });
      } // Retry mit Backoff
    }
  });
  await Promise.allSettled(promises);
}

// --- Queue Consumer für Post Sync ---
async function processSyncQueue(
  batch: MessageBatch<SyncQueueMessage>,
  env: Bindings,
  ctx: ExecutionContext
): Promise<void> {
  console.log(`Processing ${batch.messages.length} sync messages...`);
  const db = getDbClient(env.DB);
  const promises = batch.messages.map(async (message) => {
    const { platformConnectionId, triggeredByUserId } = message.body;
    console.log(
      `Sync Queue: Starting sync for connection ${platformConnectionId}`
    );
    try {
      const connection = await getConnectionDetails(db, platformConnectionId);
      if (
        !connection ||
        connection.status !== "active" ||
        !connection.accessTokenEncrypted
      ) {
        /* Log & return */ return;
      }
      const accessToken = await decryptToken(
        connection.accessTokenEncrypted,
        env
      );
      if (!accessToken) {
        await updateConnectionStatus(db, platformConnectionId, "reauth_needed");
        return;
      }

      // API Call via Service
      const mediaResult = await fetchLatestGraphMedia(
        accessToken,
        connection.platformAccountId,
        env,
        100
      ); // Env für Logging

      if (!mediaResult)
        throw new Error(`API client failed for ${platformConnectionId}`);
      if (mediaResult.error) {
        if (
          mediaResult.error instanceof GraphApiError &&
          mediaResult.error.isAuthError
        ) {
          await updateConnectionStatus(
            db,
            platformConnectionId,
            "reauth_needed"
          );
          return;
        } else {
          throw mediaResult.error;
        }
      }

      // DB Write via Service
      if (mediaResult.posts && mediaResult.posts.length > 0) {
        console.log(
          `Sync Queue: Upserting ${mediaResult.posts.length} posts for ${platformConnectionId}`
        );
        for (const post of mediaResult.posts) {
          // post ist hier BasicDisplayApiMediaItem oder GraphApiMedia
          const postData: InsertPost = {
            id: crypto.randomUUID(),
            platformConnectionId: connection.id,
            mediaId: post.id,
            likeCount: (post as any).like_count ?? 0, // Type assertion nötig oder besser prüfen
            commentsCount: (post as any).comments_count ?? 0,
            caption: post.caption,
            mediaUrl: post.media_url,
            mediaType: post.media_type,
            permalink: post.permalink,
            timestamp: new Date(post.timestamp),
            lastFetched: new Date(),
          };
          try {
            await upsertPost(db, postData);
          } catch (d1Error) {
            logErrorToAnalytics(
              env,
              "SYNC_DB_POST_ERROR",
              `Error upserting post ${post.id} during sync`,
              { platformConnectionId, mediaId: post.id, error: String(d1Error) }
            );
          }
        }
      }
      console.log(
        `Sync Queue: Finished sync successfully for connection ${platformConnectionId}`
      );
    } catch (error: any) {
      console.error(
        `Sync Queue: CRITICAL error processing message for pcId=${platformConnectionId}:`,
        error
      );
      const maxRetries = parseInt(env.QUEUE_MAX_RETRIES || "3", 10); // Ggf. anderes Retry Limit für Sync
      if (message.attempts >= maxRetries) {
        logErrorToAnalytics(
          env,
          "SYNC_FINAL_FAILURE",
          `Sync message failed all retries`,
          {
            platformConnectionId,
            attempts: message.attempts,
            error: String(error),
          }
        );
      } else {
        message.retry({ delaySeconds: 60 * (message.attempts + 1) });
      } // Längeres Backoff für Sync?
    }
  });
  await Promise.allSettled(promises);
}

// --- Scheduled Handler für Token Check ---
async function handleTokenValidityCheck(env: Bindings, ctx: ExecutionContext) {
  console.log(`Scheduled token validity check running...`);
  const db = getDbClient(env.DB);
  const now = Date.now();
  const checkThreshold = now - 23 * 60 * 60 * 1000; // 23 hours ago
  try {
    const connectionsToCheck = await getConnectionsForTokenCheck(
      db,
      checkThreshold
    ); // DB Service
    console.log(
      `Found ${connectionsToCheck.length} connections for validity check.`
    );
    for (const conn of connectionsToCheck) {
      let accessToken: string | null = null;
      if (conn.accessTokenEncrypted)
        accessToken = await decryptToken(conn.accessTokenEncrypted, env); // Token Util
      if (!accessToken) {
        await updateConnectionStatus(db, conn.id, "reauth_needed");
        logErrorToAnalytics(
          env,
          "TOKEN_CHECK_DECRYPT_FAIL",
          `Failed decrypt token`,
          { platformConnectionId: conn.id }
        );
        continue;
      }

      let isValid = false;
      try {
        if (conn.apiType === "graph") {
          const testUrl = `https://graph.facebook.com/${constants.META_API_VERSION}/me?fields=id&access_token=${accessToken}`;
          const response = await fetch(testUrl);
          if (response.ok) {
            const jsonResp = await response.json();
            const validation = GraphApiMeSchema.safeParse(jsonResp);
            if (validation.success && validation.data.id) isValid = true;
            else {
              logErrorToAnalytics(
                env,
                "TOKEN_CHECK_API_VALIDATION_FAIL",
                "Invalid /me response",
                {
                  platformConnectionId: conn.id,
                  error: validation.error?.flatten(),
                  received: jsonResp,
                }
              );
            }
          } else {
            await handleTokenCheckApiError(db, env, conn, response);
          } // Helper nutzen
        } else if (conn.apiType === "basic_display") {
          isValid = await checkBasicDisplayToken(accessToken, env); // API Service + Env
          if (!isValid) {
            await updateConnectionStatus(db, conn.id, "reauth_needed");
            logErrorToAnalytics(
              env,
              "TOKEN_CHECK_BD_FAIL",
              `Basic Display token invalid`,
              { platformConnectionId: conn.id }
            );
          }
        }
        if (isValid) {
          await db
            .update(schema.platformConnections)
            .set({ lastCheckedAt: new Date() })
            .where(eq(schema.platformConnections.id, conn.id));
        }
      } catch (fetchError) {
        logErrorToAnalytics(
          env,
          "TOKEN_CHECK_NETWORK_ERROR",
          `Network error during check`,
          { platformConnectionId: conn.id, error: String(fetchError) }
        );
      }
    }
  } catch (error) {
    console.error("CRITICAL Error during scheduled token check:", error);
    logErrorToAnalytics(
      env,
      "SCHEDULED_HANDLER_ERROR",
      `Token check handler failed`,
      { error: String(error) }
    );
  }
}
// Helper für Token Check Fehlerbehandlung
async function handleTokenCheckApiError(
  db: DrizzleD1Database<typeof schema>,
  env: Bindings,
  conn: Pick<PlatformConnection, "id">,
  response: Response
) {
  console.warn(
    `Token Check: API Error Status ${response.status} for conn ${conn.id}`
  );
  if ([400, 401, 403].includes(response.status)) {
    await updateConnectionStatus(db, conn.id, "reauth_needed"); // DB Service
    logErrorToAnalytics(
      env,
      "TOKEN_CHECK_AUTH_FAIL",
      `Token invalid or permissions revoked`,
      { platformConnectionId: conn.id, status: response.status }
    );
  } else {
    logErrorToAnalytics(
      env,
      "TOKEN_CHECK_API_ERROR",
      `API error during check, status ${response.status}`,
      { platformConnectionId: conn.id, status: response.status }
    );
  }
}

// --- Scheduled Handler für Basic Display Polling ---
async function handleBasicDisplayPolling(
  env: Bindings,
  ctx: ExecutionContext
): Promise<void> {
  console.log(`Basic Display Polling running...`);
  const db = getDbClient(env.DB);
  try {
    const connectionsToPoll = await getActiveBasicDisplayConnections(db); // DB Service
    console.log(
      `Polling ${connectionsToPoll.length} active Basic Display connections.`
    );
    for (const conn of connectionsToPoll) {
      let accessToken: string | null = null;
      try {
        if (!conn.accessTokenEncrypted) continue;
        accessToken = await decryptToken(conn.accessTokenEncrypted, env); // Token Util
        if (!accessToken) {
          await updateConnectionStatus(db, conn.id, "reauth_needed");
          continue;
        } // DB Service

        // API Call via Service
        const mediaResult = await fetchBasicDisplayMedia(accessToken, env, 50); // API Service + Env

        if (!mediaResult) throw new Error(`API client failed for ${conn.id}`);
        if (mediaResult.error) {
          if (
            mediaResult.error instanceof GraphApiError &&
            mediaResult.error.isAuthError
          ) {
            await updateConnectionStatus(db, conn.id, "reauth_needed");
          } else {
            throw mediaResult.error;
          }
          continue;
        }

        // DB Vergleich & Write via Service
        if (mediaResult.posts && mediaResult.posts.length > 0) {
          // Hole existierende IDs
          const existingMediaIds = new Set(
            (
              await db
                .select({ mediaId: schema.posts.mediaId })
                .from(schema.posts)
                .where(eq(schema.posts.platformConnectionId, conn.id))
                .orderBy(desc(schema.posts.timestamp))
                .limit(200) // Schaue weiter zurück beim Poll
                .all()
            ).map((p) => p.mediaId)
          );

          let newPostsFound = 0;
          for (const post of mediaResult.posts) {
            // post ist BasicDisplayApiMediaItem
            if (!existingMediaIds.has(post.id)) {
              newPostsFound++;
              const postData: InsertPost = {
                id: crypto.randomUUID(),
                platformConnectionId: conn.id,
                mediaId: post.id,
                likeCount: 0,
                commentsCount: 0,
                caption: post.caption,
                mediaUrl: post.media_url,
                mediaType: post.media_type,
                permalink: post.permalink,
                timestamp: new Date(post.timestamp),
                lastFetched: new Date(),
              };
              try {
                await upsertPost(db, postData);
              } catch (d1Error) {
                // DB Service
                logErrorToAnalytics(
                  env,
                  "POLLING_DB_POST_ERROR",
                  `Error upserting polled post ${post.id}`,
                  {
                    platformConnectionId: conn.id,
                    mediaId: post.id,
                    error: String(d1Error),
                  }
                );
              }
            }
          }
          if (newPostsFound > 0)
            console.log(
              `Polling: Found ${newPostsFound} new posts for ${conn.id}.`
            );
        }
        // Optional: Update lastPolledAt
        // await db.update(schema.platformConnections).set({ lastPolledAt: new Date() }).where(eq(schema.platformConnections.id, conn.id));
      } catch (connError: any) {
        console.error(
          `Polling: Unhandled error processing connection ${conn.id}:`,
          connError
        );
        logErrorToAnalytics(
          env,
          "POLLING_CONN_ERROR",
          `Unhandled error polling connection`,
          { platformConnectionId: conn.id, error: String(connError) }
        );
      }
    }
  } catch (error) {
    console.error("CRITICAL Error during Basic Display Polling:", error);
    logErrorToAnalytics(
      env,
      "POLLING_HANDLER_ERROR",
      `Polling handler failed`,
      { error: String(error) }
    );
  }
}

/**
 * Haupt-Handler für Scheduled Events (Cron Triggers).
 * Leitet die Anfrage basierend auf dem Cron-String an die zuständige Funktion weiter.
 */
async function handleScheduled(
  event: ScheduledEvent,
  env: Bindings,
  ctx: ExecutionContext
) {
  console.log(`Scheduled event triggered by cron: ${event.cron}`);
  // Nutze waitUntil, um sicherzustellen, dass die Verarbeitung Zeit hat, auch wenn der Trigger kurzlebig ist
  if (event.cron === "*/5 * * * *") {
    // Alle 5 Minuten
    ctx.waitUntil(handleBasicDisplayPolling(env, ctx));
  } else if (event.cron === "5 3 * * *") {
    // Täglich um 03:05 UTC
    ctx.waitUntil(handleTokenValidityCheck(env, ctx));
  } else {
    console.warn(`Scheduled event triggered by unexpected cron: ${event.cron}`);
    // Logge diesen unerwarteten Trigger auch
    logErrorToAnalytics(
      env,
      "UNKNOWN_CRON",
      `Unexpected cron trigger received`,
      { cron: event.cron }
    );
  }
}

// --- Exports ---
export { DebouncerDO }; // Exportiere DO Klasse für wrangler.toml
export default {
  fetch: app.fetch, // Hono Request Handler
  queue: async (
    batch: MessageBatch<any>,
    env: Bindings,
    ctx: ExecutionContext
  ) => {
    // Haupt Queue Router
    try {
      // Leite an den richtigen Consumer weiter basierend auf Queue-Namen
      if (batch.queue === "pagination-queue")
        await processPaginationQueue(
          batch as MessageBatch<PaginationQueueMessage>,
          env,
          ctx
        );
      else if (batch.queue === "sync-queue")
        await processSyncQueue(
          batch as MessageBatch<SyncQueueMessage>,
          env,
          ctx
        );
      // Optional: Consumer für DLQs hier hinzufügen, falls gewünscht
      // else if (batch.queue === 'pagination-dlq') await processPaginationDLQ(batch, env, ctx);
      // else if (batch.queue === 'sync-dlq') await processSyncDLQ(batch, env, ctx);
      else {
        console.error(`Msg from unknown queue: ${batch.queue}`);
        logErrorToAnalytics(
          env,
          "UNKNOWN_QUEUE_MESSAGE",
          `Msg from queue ${batch.queue}`,
          { count: batch.messages.length }
        );
        batch.retryAll();
      }
    } catch (e) {
      console.error(`Unhandled queue processor error for ${batch.queue}:`, e);
      logErrorToAnalytics(
        env,
        "QUEUE_PROCESSOR_FATAL",
        `Unhandled error in queue ${batch.queue}`,
        { error: String(e) }
      );
      batch.retryAll();
    }
  },
  scheduled: handleScheduled, // Scheduled Event Handler
};

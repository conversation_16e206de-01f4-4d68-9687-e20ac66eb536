// src/token-utils.ts

// Helper: Bytes zu Hex / Hex zu Bytes
function bytesToHex(bytes: Uint8Array): string {
  return Array.from(bytes)
    .map((b) => b.toString(16).padStart(2, "0"))
    .join("");
}
function hexToBytes(hex: string): Uint8Array {
  if (hex.length % 2 !== 0) {
    throw new Error("Invalid hex string length.");
  }
  const bytes = new Uint8Array(hex.length / 2);
  for (let i = 0; i < hex.length; i += 2) {
    bytes[i / 2] = parseInt(hex.substring(i, i + 2), 16);
    if (isNaN(bytes[i / 2])) {
      throw new Error("Invalid hex character found.");
    }
  }
  return bytes;
}

// Holt den CryptoKey sicher aus der Umgebungsvariable
async function getCryptoKey(env: {
  ENCRYPTION_KEY: string;
}): Promise<CryptoKey> {
  if (!env.ENCRYPTION_KEY || env.ENCRYPTION_KEY.length !== 64) {
    console.error(
      "ENCRYPTION_KEY invalid or missing from environment variables."
    );
    throw new Error(
      "Invalid ENCRYPTION_KEY format (must be 64 hex chars / 32 bytes)."
    );
  }
  try {
    const keyBytes = hexToBytes(env.ENCRYPTION_KEY);
    return await crypto.subtle.importKey(
      "raw",
      keyBytes,
      { name: "AES-GCM" },
      false, // non-exportable
      ["encrypt", "decrypt"]
    );
  } catch (e) {
    console.error("Failed to import encryption key:", e);
    throw new Error("Could not import encryption key.");
  }
}

// Encrypts token using AES-GCM and returns base64(iv + ciphertext)
export async function encryptToken(
  token: string,
  env: { ENCRYPTION_KEY: string }
): Promise<string | null> {
  if (!token) return null;
  try {
    const key = await getCryptoKey(env);
    const iv = crypto.getRandomValues(new Uint8Array(12)); // AES-GCM standard IV size is 12 bytes
    const encodedToken = new TextEncoder().encode(token);

    const encryptedData = await crypto.subtle.encrypt(
      { name: "AES-GCM", iv: iv },
      key,
      encodedToken
    );

    // Combine IV and ciphertext
    const combined = new Uint8Array(iv.length + encryptedData.byteLength);
    combined.set(iv, 0);
    combined.set(new Uint8Array(encryptedData), iv.length);

    // Encode to Base64 for storing as string
    let base64String = btoa(String.fromCharCode(...combined));
    return base64String;
  } catch (e) {
    console.error("Encryption failed:", e);
    return null;
  }
}

// Decrypts base64(iv + ciphertext) using AES-GCM
export async function decryptToken(
  encryptedTokenB64: string,
  env: { ENCRYPTION_KEY: string }
): Promise<string | null> {
  if (!encryptedTokenB64) return null;
  try {
    const key = await getCryptoKey(env);
    // Decode from Base64
    const combined = Uint8Array.from(atob(encryptedTokenB64), (c) =>
      c.charCodeAt(0)
    );

    if (combined.length < 13) {
      // 12 bytes IV + at least 1 byte data
      console.error("Decryption failed: Encrypted data too short.");
      return null;
    }

    const iv = combined.slice(0, 12); // Extract IV (first 12 bytes)
    const encryptedData = combined.slice(12); // Extract actual ciphertext

    const decryptedData = await crypto.subtle.decrypt(
      { name: "AES-GCM", iv: iv },
      key,
      encryptedData
    );

    return new TextDecoder().decode(decryptedData);
  } catch (e) {
    console.error(
      "Decryption failed (likely wrong key, tampered data, or invalid format):",
      e
    );
    return null;
  }
}

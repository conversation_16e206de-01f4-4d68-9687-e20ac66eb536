// src/database-service.ts

/**
 * <PERSON>ses Modul kapselt alle Datenbankinteraktionen für die D1-Datenbank
 * unter Verwendung von Drizzle ORM.
 */

import { drizzle, DrizzleD1Database } from "drizzle-orm/d1";
import { eq, and, sql, desc, or, lt, isNull, inArray } from "drizzle-orm";
import * as schema from "./db/schema";
import {
  PlatformConnection,
  ApiKey,
  Post,
  Comment,
  InsertPost,
  InsertComment,
  Platform,
  User,
  Feed,
  ConnectionStatus,
  ApiKeyStatus,
  ApiType,
} from "./types";

// --- Typ für den Drizzle D1 Client ---
type DbClient = DrizzleD1Database<typeof schema>;

// --- Initialisierung ---

/**
 * Erstellt und gibt eine Drizzle D1 Client Instanz zurück.
 * @param d1 - Die D1 Datenbank Binding aus der Umgebung.
 * @returns Eine initialisierte Drizzle Client Instanz.
 */
export function getDbClient(d1: D1Database): DbClient {
  // <PERSON><PERSON> sicher, dass das Schema übergeben wird, damit Typen korrekt funktionieren
  return drizzle(d1, { schema });
}

// --- Platform Connections ---

/**
 * Findet alle aktiven PlatformConnection IDs für einen gegebenen Plattform-Nutzer.
 * Nützlich, um eingehende Webhooks den richtigen internen Verbindungen zuzuordnen.
 * @param db - Der Drizzle DB Client.
 * @param platform - Die Plattform ('instagram', 'facebook', etc.).
 * @param platformUserId - Die User ID auf der externen Plattform.
 * @returns Ein Array von Objekten mit der internen Connection ID.
 */
export async function findActiveConnectionsByPlatformUserId(
  db: DbClient,
  platform: Platform,
  platformUserId: string
): Promise<{ id: string }[]> {
  try {
    return await db
      .select({ id: schema.platformConnections.id })
      .from(schema.platformConnections)
      .where(
        and(
          eq(schema.platformConnections.platform, platform),
          eq(schema.platformConnections.platformAccountId, platformUserId),
          eq(schema.platformConnections.status, "active")
        )
      )
      .all();
  } catch (e) {
    console.error(
      `DB_SERVICE: Error finding active connections for ${platform} user ${platformUserId}:`,
      e
    );
    throw e; // Fehler weiterleiten für besseres Debugging im Aufrufer
  }
}

/**
 * Holt alle Details für eine spezifische Platform Connection anhand ihrer internen ID.
 * @param db - Der Drizzle DB Client.
 * @param platformConnectionId - Die interne ID der Platform Connection.
 * @returns Das PlatformConnection Objekt oder null, wenn nicht gefunden.
 */
export async function getConnectionDetails(
  db: DbClient,
  platformConnectionId: string
): Promise<PlatformConnection | null> {
  try {
    const result = await db
      .select()
      .from(schema.platformConnections)
      .where(eq(schema.platformConnections.id, platformConnectionId))
      .get(); // .get() holt max. 1 Ergebnis
    return result ?? null;
  } catch (e) {
    console.error(
      `DB_SERVICE: Error getting connection details for ${platformConnectionId}:`,
      e
    );
    throw e;
  }
}

/**
 * Aktualisiert den Status einer Platform Connection und setzt den `lastCheckedAt` Zeitstempel.
 * @param db - Der Drizzle DB Client.
 * @param platformConnectionId - Die ID der zu aktualisierenden Connection.
 * @param status - Der neue Status ('active', 'inactive', 'reauth_needed', 'expired').
 */
export async function updateConnectionStatus(
  db: DbClient,
  platformConnectionId: string,
  status: ConnectionStatus
): Promise<void> {
  try {
    const result = await db
      .update(schema.platformConnections)
      .set({ status: status, lastCheckedAt: new Date() }) // lastCheckedAt immer aktualisieren
      .where(eq(schema.platformConnections.id, platformConnectionId))
      .run(); // .run() für Updates ohne Rückgabewert in D1
    console.log(
      `DB_SERVICE: Updated status for connection ${platformConnectionId} to ${status}. Rows affected: ${result.meta.changes}`
    );
  } catch (e) {
    console.error(
      `DB_SERVICE: Failed to update status for connection ${platformConnectionId} to ${status}:`,
      e
    );
    // Fehler loggen, aber nicht unbedingt weiterwerfen, um den aufrufenden Prozess (z.B. Scheduled Handler) nicht komplett abzubrechen.
    // LogErrorToAnalytics könnte hier aufgerufen werden.
  }
}

/**
 * Holt alle aktiven Instagram Basic Display Connections.
 * @param db - Der Drizzle DB Client.
 * @returns Ein Array von Objekten mit id, platformAccountId und verschlüsseltem Token.
 */
export async function getActiveBasicDisplayConnections(
  db: DbClient
): Promise<
  Pick<
    PlatformConnection,
    "id" | "platformAccountId" | "accessTokenEncrypted"
  >[]
> {
  try {
    return await db
      .select({
        id: schema.platformConnections.id,
        platformAccountId: schema.platformConnections.platformAccountId,
        accessTokenEncrypted: schema.platformConnections.accessTokenEncrypted,
      })
      .from(schema.platformConnections)
      .where(
        and(
          eq(schema.platformConnections.platform, "instagram"), // Nur Instagram
          eq(schema.platformConnections.apiType, "basic_display"),
          eq(schema.platformConnections.status, "active")
        )
      )
      .all();
  } catch (e) {
    console.error(
      `DB_SERVICE: Error getting active basic display connections:`,
      e
    );
    throw e;
  }
}

/**
 * Holt alle aktiven Instagram/Facebook Connections, deren Tokens überprüft werden müssen.
 * @param db - Der Drizzle DB Client.
 * @param checkThreshold - Zeitstempel (ms), nur Tokens prüfen, die älter als dieser sind.
 * @returns Ein Array von Connection-Objekten mit den für den Check nötigen Feldern.
 */
export async function getConnectionsForTokenCheck(
  db: DbClient,
  checkThreshold: number
): Promise<
  Pick<
    PlatformConnection,
    "id" | "platform" | "apiType" | "accessTokenEncrypted" | "tokenExpiresAt"
  >[]
> {
  try {
    return await db
      .select({
        id: schema.platformConnections.id,
        platform: schema.platformConnections.platform,
        apiType: schema.platformConnections.apiType,
        accessTokenEncrypted: schema.platformConnections.accessTokenEncrypted,
        tokenExpiresAt: schema.platformConnections.tokenExpiresAt,
      })
      .from(schema.platformConnections)
      .where(
        and(
          or(
            // Nur Instagram oder Facebook (TikTok hat ggf. andere Logik)
            eq(schema.platformConnections.platform, "instagram"),
            eq(schema.platformConnections.platform, "facebook")
          ),
          eq(schema.platformConnections.status, "active"), // Nur aktive prüfen
          or(
            // Wenn lange nicht geprüft ODER bald abläuft
            isNull(schema.platformConnections.lastCheckedAt),
            lt(
              schema.platformConnections.lastCheckedAt,
              new Date(checkThreshold)
            )
            // Optional: Ablaufdatum prüfen (hier weggelassen für Einfachheit)
            // lt(schema.platformConnections.tokenExpiresAt, new Date(now + 5 * 24 * 60 * 60 * 1000))
          )
        )
      )
      .all();
  } catch (e) {
    console.error(`DB_SERVICE: Error getting connections for token check:`, e);
    throw e;
  }
}

/**
 * Prüft, ob ein gegebener Nutzer der Besitzer des Feeds ist, zu dem eine Platform Connection gehört.
 * @param db - Der Drizzle DB Client.
 * @param platformConnectionId - Die ID der Platform Connection.
 * @param userId - Die ID des Nutzers (aus deinem Auth-System).
 * @returns true, wenn der Nutzer der Besitzer ist, sonst false.
 */
export async function isConnectionOwnedByUser(
  db: DbClient,
  platformConnectionId: string,
  userId: string
): Promise<boolean> {
  try {
    const result = await db
      .select({ ownerUserId: schema.feeds.userId })
      .from(schema.platformConnections)
      .innerJoin(
        schema.feeds,
        eq(schema.platformConnections.feedId, schema.feeds.id)
      )
      .where(eq(schema.platformConnections.id, platformConnectionId))
      .limit(1)
      .get();
    return result?.ownerUserId === userId;
  } catch (e) {
    console.error(
      `DB_SERVICE: Error checking ownership for connection ${platformConnectionId} and user ${userId}:`,
      e
    );
    throw e; // Fehler bei der Prüfung sollte nach außen signalisiert werden
  }
}

// --- API Keys ---

/**
 * Validates an API Key against the database.
 * @param db - The Drizzle DB Client.
 * @param apiKey - The API Key to validate.
 * @returns An object containing validation results and feed information.
 */
export async function validateApiKey(
  db: DbClient,
  apiKey: string
): Promise<{
  isValid: boolean;
  feedId?: string;
  organizationId?: string;
  error?: string;
  status?: number;
}> {
  try {
    // Select specific fields
    const keyRecord = await db
      .select({
        feedId: schema.apiKeys.feedId,
        status: schema.apiKeys.status,
        expiresAt: schema.apiKeys.expiresAt,
      })
      .from(schema.apiKeys)
      .where(eq(schema.apiKeys.key, apiKey))
      .get();

    if (!keyRecord) {
      return { isValid: false, error: "Invalid API key", status: 401 };
    }
    if (keyRecord.status !== "active") {
      return { isValid: false, error: "API key is inactive", status: 403 };
    }
    if (keyRecord.expiresAt && keyRecord.expiresAt < new Date()) {
      return { isValid: false, error: "API key has expired", status: 403 };
    }

    // Get the organization ID for the feed
    const feedRecord = await db
      .select({ organizationId: schema.feeds.organizationId })
      .from(schema.feeds)
      .where(eq(schema.feeds.id, keyRecord.feedId))
      .get();

    if (!feedRecord) {
      return { isValid: false, error: "Feed not found", status: 404 };
    }

    return {
      isValid: true,
      feedId: keyRecord.feedId,
      organizationId: feedRecord.organizationId,
    };
  } catch (e) {
    console.error("DB_SERVICE: API key validation DB error:", e);
    return {
      isValid: false,
      error: "Internal server error during authentication",
      status: 500,
    };
  }
}

// --- Posts & Comments ---

/**
 * Fügt einen neuen Post ein oder aktualisiert einen bestehenden Post
 * basierend auf platformConnectionId und mediaId (Upsert).
 * @param db - Der Drizzle DB Client.
 * @param postData - Die einzufügenden/aktualisierenden Post-Daten.
 */
export async function upsertPost(
  db: DbClient,
  postData: InsertPost
): Promise<void> {
  try {
    // Stelle sicher, dass eine ID vorhanden ist (könnte auch von DB generiert werden)
    postData.id = postData.id || crypto.randomUUID();
    // Setze Default-Werte, falls nicht vorhanden (obwohl DB Defaults hat)
    postData.likeCount = postData.likeCount ?? 0;
    postData.commentsCount = postData.commentsCount ?? 0;

    await db
      .insert(schema.posts)
      .values(postData)
      .onConflictDoUpdate({
        // Wichtig: Target muss auf einem UNIQUE Index oder PRIMARY KEY basieren
        target: [schema.posts.platformConnectionId, schema.posts.mediaId],
        set: {
          // Nur diese Felder werden bei einem Konflikt aktualisiert
          likeCount: postData.likeCount,
          commentsCount: postData.commentsCount,
          caption: postData.caption,
          mediaUrl: postData.mediaUrl,
          mediaType: postData.mediaType,
          permalink: postData.permalink,
          // timestamp sollte sich normalerweise nicht ändern
          lastWebhookUpdate: postData.lastWebhookUpdate,
          lastFetched: postData.lastFetched,
        },
      });
  } catch (e) {
    console.error(
      `DB_SERVICE: Error upserting post ${postData.mediaId} for conn ${postData.platformConnectionId}:`,
      e
    );
    throw e; // Fehler weitergeben, damit Aufrufer reagieren kann
  }
}

/**
 * Fügt einen neuen Kommentar ein oder aktualisiert einen bestehenden Kommentar
 * basierend auf platformConnectionId und commentId (Upsert).
 * @param db - Der Drizzle DB Client.
 * @param commentData - Die einzufügenden/aktualisierenden Kommentar-Daten.
 */
export async function upsertComment(
  db: DbClient,
  commentData: InsertComment
): Promise<void> {
  try {
    commentData.id = commentData.id || crypto.randomUUID(); // Eigene ID setzen

    await db
      .insert(schema.comments)
      .values(commentData)
      .onConflictDoUpdate({
        // Wichtig: Target muss auf einem UNIQUE Index oder PRIMARY KEY basieren
        target: [
          schema.comments.platformConnectionId,
          schema.comments.commentId,
        ],
        set: {
          text: commentData.text, // Aktualisiere nur den Text bei Konflikt
          // userId und username könnten sich theoretisch ändern, aber selten
          // timestamp sollte konstant bleiben
        },
      });
  } catch (e) {
    console.error(
      `DB_SERVICE: Error upserting comment ${commentData.commentId} for conn ${commentData.platformConnectionId}:`,
      e
    );
    throw e;
  }
}

/**
 * Löscht einen spezifischen Kommentar anhand seiner Platform Connection ID und Kommentar ID.
 * @param db - Der Drizzle DB Client.
 * @param platformConnectionId - Die interne ID der Platform Connection.
 * @param commentId - Die ID des Kommentars auf der externen Plattform.
 */
export async function deleteComment(
  db: DbClient,
  platformConnectionId: string,
  commentId: string
): Promise<void> {
  try {
    const result = await db
      .delete(schema.comments)
      .where(
        and(
          eq(schema.comments.platformConnectionId, platformConnectionId),
          eq(schema.comments.commentId, commentId)
        )
      )
      .run();
    console.log(
      `DB_SERVICE: Deleted comment ${commentId} for conn ${platformConnectionId}. Rows affected: ${result.meta.changes}`
    );
  } catch (e) {
    console.error(
      `DB_SERVICE: Error deleting comment ${commentId} for conn ${platformConnectionId}:`,
      e
    );
    throw e;
  }
}

/**
 * Holt die letzten Posts für einen bestimmten Feed, optional gefiltert nach Plattform.
 * @param db - Der Drizzle DB Client.
 * @param feedId - Die ID des Feeds.
 * @param limit - Die maximale Anzahl der zurückzugebenden Posts.
 * @param platformFilter - Optional: Nur Posts von dieser Plattform holen.
 * @returns Ein Array von Post-Objekten.
 */
export async function getPostsForFeed(
  db: DbClient,
  feedId: string,
  limit: number,
  platformFilter?: Platform
): Promise<Post[]> {
  try {
    // 1. Finde relevante, aktive Connections für den Feed
    const connections = await db
      .select({ id: schema.platformConnections.id })
      .from(schema.platformConnections)
      .where(
        and(
          eq(schema.platformConnections.feedId, feedId),
          eq(schema.platformConnections.status, "active"),
          platformFilter
            ? eq(schema.platformConnections.platform, platformFilter)
            : undefined
        )
      )
      .all();
    if (connections.length === 0) {
      return []; // Keine passenden Connections gefunden
    }
    const connectionIds = connections.map((conn) => conn.id);

    // 2. Hole Posts für diese Connections
    return await db
      .select()
      .from(schema.posts)
      // Nutze Drizzle's `inArray` Operator
      .where(inArray(schema.posts.platformConnectionId, connectionIds))
      .orderBy(desc(schema.posts.timestamp)) // Neueste zuerst
      .limit(limit)
      .all();
  } catch (e) {
    console.error(`DB_SERVICE: Error getting posts for feed ${feedId}:`, e);
    throw e;
  }
}

/**
 * Checks if a feed belongs to an organization
 * @param db - The Drizzle DB Client
 * @param feedId - The ID of the feed
 * @param organizationId - The organization ID from PropelAuth
 * @returns true if the feed belongs to the organization
 */
export async function isFeedOwnedByOrganization(
  db: DbClient,
  feedId: string,
  organizationId: string
): Promise<boolean> {
  try {
    const result = await db
      .select({ orgId: schema.feeds.organizationId })
      .from(schema.feeds)
      .where(eq(schema.feeds.id, feedId))
      .get();

    return result?.orgId === organizationId;
  } catch (e) {
    console.error(
      `DB_SERVICE: Error checking feed ownership for feed ${feedId} and org ${organizationId}:`,
      e
    );
    return false;
  }
}

/**
 * Checks if a connection belongs to an organization via its feed
 * @param db - The Drizzle DB Client
 * @param connectionId - The ID of the platform connection
 * @param organizationId - The organization ID from PropelAuth
 * @returns true if the connection belongs to the organization
 */
export async function isConnectionOwnedByOrganization(
  db: DbClient,
  connectionId: string,
  organizationId: string
): Promise<boolean> {
  try {
    const result = await db
      .select({ feedId: schema.platformConnections.feedId })
      .from(schema.platformConnections)
      .where(eq(schema.platformConnections.id, connectionId))
      .get();

    if (!result?.feedId) return false;

    return await isFeedOwnedByOrganization(db, result.feedId, organizationId);
  } catch (e) {
    console.error(
      `DB_SERVICE: Error checking connection ownership for connection ${connectionId} and org ${organizationId}:`,
      e
    );
    return false;
  }
}
